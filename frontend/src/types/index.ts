// Core types for MiCA Therapy Simulation

export interface Message {
  id: string;
  conversationId: string;
  sender: 'therapist' | 'patient';
  content: string;
  timestamp: string;
  metadata?: MessageMetadata;
}

export interface MessageMetadata {
  sentiment?: 'positive' | 'negative' | 'neutral';
  motivationLevel?: 'low' | 'medium' | 'high';
  engagementLevel?: 'low' | 'medium' | 'high';
  confidence?: number;
}

export interface AgentThought {
  id: string;
  agentType: 'therapist' | 'patient';
  content: string;
  timestamp: string;
  messageId?: string;
  type: 'analysis' | 'planning' | 'reflection' | 'decision';
}

export interface Conversation {
  id: string;
  status: 'active' | 'completed' | 'paused';
  startTime: string;
  endTime?: string;
  config: ConversationConfig;
  messages: Message[];
  thoughts: AgentThought[];
  analytics?: ConversationAnalytics;
}

export interface ConversationConfig {
  maxTurns: number;
  therapistPersona?: string;
  patientPersona?: string;
  scenario?: string;
  autoAdvance?: boolean;
  responseDelay?: number;
}

export interface ConversationAnalytics {
  totalTurns: number;
  averageResponseTime: number;
  sentimentProgression: Array<{
    turn: number;
    sentiment: string;
    confidence: number;
  }>;
  engagementTrend: Array<{
    turn: number;
    level: string;
  }>;
}

export interface WebSocketMessage {
  type: 'welcome' | 'message' | 'thought' | 'error' | 'conversation_update' | 'agent_response';
  data?: any;
  timestamp: string;
  conversationId?: string;
}

export interface AgentResponse {
  message: string;
  thought: string;
  metadata: MessageMetadata;
  processingTime: number;
}

export interface ConnectionStatus {
  status: 'connected' | 'disconnected' | 'connecting' | 'error';
  lastConnected?: string;
  reconnectAttempts?: number;
}
