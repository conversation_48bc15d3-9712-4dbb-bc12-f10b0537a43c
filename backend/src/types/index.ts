// Backend types for MiCA Therapy Simulation

export interface AgentConfig {
  model: string;
  temperature: number;
  maxTokens: number;
  systemPrompt: string;
  persona?: string;
}

// Therapeutic Framework Types
export interface TherapeuticApproachInfo {
  id: 'motivational-interviewing' | 'cognitive-behavioral-therapy';
  name: string;
  selectedTechnique: {
    id: string;
    name: string;
    description: string;
  };
}

export interface ReadinessScore {
  score: number; // 1-10 scale
  factors: {
    sentiment: {
      value: 'positive' | 'negative' | 'neutral';
      weight: number;
      contribution: number;
    };
    motivation: {
      value: 'low' | 'medium' | 'high';
      weight: number;
      contribution: number;
    };
    engagement: {
      value: 'low' | 'medium' | 'high';
      weight: number;
      contribution: number;
    };
  };
  reasoning: string;
  recommendedApproach: 'CBT' | 'MI';
}

export interface PatientAnalysis {
  sentiment: 'positive' | 'negative' | 'neutral';
  motivationLevel: 'low' | 'medium' | 'high';
  engagementLevel: 'low' | 'medium' | 'high';
  readinessScore: ReadinessScore;
}

export interface TherapistAgent extends AgentConfig {
  type: 'therapist';
  analysisPrompts: {
    sentiment: string;
    motivation: string;
    engagement: string;
  };
}

export interface PatientAgent extends AgentConfig {
  type: 'patient';
  emotionalState: EmotionalState;
  backstory?: string;
  currentConcerns?: string[];
}

export interface EmotionalState {
  mood: 'depressed' | 'anxious' | 'neutral' | 'hopeful' | 'frustrated';
  energy: 'low' | 'medium' | 'high';
  openness: 'closed' | 'guarded' | 'open' | 'very_open';
  trust: number; // 0-100
}

export interface ConversationContext {
  id: string;
  messages: Array<{
    id: string;
    conversationId: string;
    sender: 'therapist' | 'patient';
    content: string;
    thinking: string;
    metadata: {
      confidence: number;
      processingTime: number;
      patientAnalysis?: PatientAnalysis;
      therapeuticApproach?: TherapeuticApproachInfo;
    };
    timestamp: string;
  }>;
  currentTurn: number;
  maxTurns: number;
  status: 'active' | 'completed' | 'paused';
}

export interface AgentResponse {
  message: string;
  thinking: string;
  metadata: {
    confidence: number;
    processingTime: number;
  };
}

export interface TherapistResponse extends AgentResponse {
  metadata: AgentResponse['metadata'] & {
    patientAnalysis?: PatientAnalysis;
    therapeuticApproach?: TherapeuticApproachInfo;
  };
}

export interface PatientResponse extends AgentResponse {
  // Patient responses don't include self-analysis
}

export interface OpenAIRequest {
  messages: Array<{
    role: 'system' | 'user' | 'assistant';
    content: string;
  }>;
  temperature: number;
  max_completion_tokens: number;
}

export interface DatabaseConversation {
  id: string;
  status: string;
  config: any;
  created_at: string;
  updated_at: string;
}

export interface DatabaseMessage {
  id: string;
  conversation_id: string;
  sender: string;
  content: string;
  metadata: any;
  created_at: string;
}

export interface DatabaseThought {
  id: string;
  conversation_id: string;
  agent_type: string;
  content: string;
  message_id?: string;
  type: string;
  created_at: string;
}

export interface WebSocketClient {
  id: string;
  socket: any;
  conversationId?: string;
  lastActivity: string;
}

export interface ConversationOrchestrator {
  conversationId: string;
  therapist: TherapistAgent;
  patient: PatientAgent;
  context: ConversationContext;
  clients: WebSocketClient[];
}
