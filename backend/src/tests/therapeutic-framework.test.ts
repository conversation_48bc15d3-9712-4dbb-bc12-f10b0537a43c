// Tests for Therapeutic Framework - MiCA Therapy Simulation
import { TechniqueSelector } from '../services/technique-selector';
import { PatientAnalysis } from '../types/index';
import { readinessWeights, therapeuticApproaches } from '../data/therapeutic-approaches';

// Mock OpenAI service for testing
class MockOpenAIService {
  calculateReadinessScore(
    sentiment: 'positive' | 'negative' | 'neutral',
    motivation: 'low' | 'medium' | 'high',
    engagement: 'low' | 'medium' | 'high'
  ) {
    // Calculate weighted contributions
    const sentimentScore = readinessWeights.sentiment[sentiment];
    const motivationScore = readinessWeights.motivation[motivation];
    const engagementScore = readinessWeights.engagement[engagement];

    const sentimentContribution = sentimentScore * readinessWeights.sentiment.weight;
    const motivationContribution = motivationScore * readinessWeights.motivation.weight;
    const engagementContribution = engagementScore * readinessWeights.engagement.weight;

    // Calculate final score (scale 1-10)
    const rawScore = sentimentContribution + motivationContribution + engagementContribution;
    const normalizedScore = Math.round(((rawScore - 1) / 2) * 9 + 1);
    const finalScore = Math.max(1, Math.min(10, normalizedScore));

    // Determine recommended approach
    const recommendedApproach = finalScore >= 8 ? 'CBT' : 'MI';

    return {
      score: finalScore,
      factors: {
        sentiment: {
          value: sentiment,
          weight: readinessWeights.sentiment.weight,
          contribution: sentimentContribution
        },
        motivation: {
          value: motivation,
          weight: readinessWeights.motivation.weight,
          contribution: motivationContribution
        },
        engagement: {
          value: engagement,
          weight: readinessWeights.engagement.weight,
          contribution: engagementContribution
        }
      },
      reasoning: `Score ${finalScore}/10: Patient demonstrates ${sentiment} emotional state, ${motivation} motivation for change, and ${engagement} engagement in therapy.`,
      recommendedApproach
    };
  }
}

describe('Therapeutic Framework', () => {
  let openaiService: MockOpenAIService;
  let techniqueSelector: TechniqueSelector;

  beforeEach(() => {
    openaiService = new MockOpenAIService();
    techniqueSelector = new TechniqueSelector();
  });

  describe('Readiness Score Calculation', () => {
    it('should calculate correct readiness score for high readiness patient', () => {
      const readinessScore = openaiService.calculateReadinessScore('positive', 'high', 'high');
      
      expect(readinessScore.score).toBeGreaterThanOrEqual(8);
      expect(readinessScore.recommendedApproach).toBe('CBT');
      expect(readinessScore.factors.sentiment.value).toBe('positive');
      expect(readinessScore.factors.motivation.value).toBe('high');
      expect(readinessScore.factors.engagement.value).toBe('high');
    });

    it('should calculate correct readiness score for low readiness patient', () => {
      const readinessScore = openaiService.calculateReadinessScore('negative', 'low', 'low');
      
      expect(readinessScore.score).toBeLessThan(8);
      expect(readinessScore.recommendedApproach).toBe('MI');
      expect(readinessScore.factors.sentiment.value).toBe('negative');
      expect(readinessScore.factors.motivation.value).toBe('low');
      expect(readinessScore.factors.engagement.value).toBe('low');
    });

    it('should calculate correct readiness score for medium readiness patient', () => {
      const readinessScore = openaiService.calculateReadinessScore('neutral', 'medium', 'medium');
      
      expect(readinessScore.score).toBeGreaterThanOrEqual(1);
      expect(readinessScore.score).toBeLessThanOrEqual(10);
      expect(readinessScore.factors.sentiment.value).toBe('neutral');
      expect(readinessScore.factors.motivation.value).toBe('medium');
      expect(readinessScore.factors.engagement.value).toBe('medium');
    });

    it('should use correct weights in calculation', () => {
      const readinessScore = openaiService.calculateReadinessScore('positive', 'high', 'medium');
      
      expect(readinessScore.factors.sentiment.weight).toBe(readinessWeights.sentiment.weight);
      expect(readinessScore.factors.motivation.weight).toBe(readinessWeights.motivation.weight);
      expect(readinessScore.factors.engagement.weight).toBe(readinessWeights.engagement.weight);
    });

    it('should generate meaningful reasoning', () => {
      const readinessScore = openaiService.calculateReadinessScore('positive', 'high', 'high');
      
      expect(readinessScore.reasoning).toContain('positive emotional state');
      expect(readinessScore.reasoning).toContain('high motivation for change');
      expect(readinessScore.reasoning).toContain('high engagement in therapy');
    });
  });

  describe('Approach Selection', () => {
    it('should select CBT for high readiness scores', () => {
      const patientAnalysis: PatientAnalysis = {
        sentiment: 'positive',
        motivationLevel: 'high',
        engagementLevel: 'high',
        readinessScore: {
          score: 9,
          factors: {
            sentiment: { value: 'positive', weight: 0.3, contribution: 0.9 },
            motivation: { value: 'high', weight: 0.4, contribution: 1.2 },
            engagement: { value: 'high', weight: 0.3, contribution: 0.9 }
          },
          reasoning: 'High readiness for CBT',
          recommendedApproach: 'CBT'
        }
      };

      const result = techniqueSelector.selectApproachAndTechnique(patientAnalysis);
      
      expect(result.id).toBe('cognitive-behavioral-therapy');
      expect(result.name).toBe('Cognitive Behavioral Therapy');
      expect(result.selectedTechnique).toBeDefined();
    });

    it('should select MI for low readiness scores', () => {
      const patientAnalysis: PatientAnalysis = {
        sentiment: 'negative',
        motivationLevel: 'low',
        engagementLevel: 'low',
        readinessScore: {
          score: 3,
          factors: {
            sentiment: { value: 'negative', weight: 0.3, contribution: 0.3 },
            motivation: { value: 'low', weight: 0.4, contribution: 0.4 },
            engagement: { value: 'low', weight: 0.3, contribution: 0.3 }
          },
          reasoning: 'Low readiness, needs MI',
          recommendedApproach: 'MI'
        }
      };

      const result = techniqueSelector.selectApproachAndTechnique(patientAnalysis);
      
      expect(result.id).toBe('motivational-interviewing');
      expect(result.name).toBe('Motivational Interviewing');
      expect(result.selectedTechnique).toBeDefined();
    });
  });

  describe('Technique Selection', () => {
    it('should select appropriate MI techniques for resistant patients', () => {
      const patientAnalysis: PatientAnalysis = {
        sentiment: 'negative',
        motivationLevel: 'low',
        engagementLevel: 'low',
        readinessScore: {
          score: 2,
          factors: {
            sentiment: { value: 'negative', weight: 0.3, contribution: 0.3 },
            motivation: { value: 'low', weight: 0.4, contribution: 0.4 },
            engagement: { value: 'low', weight: 0.3, contribution: 0.3 }
          },
          reasoning: 'Patient shows resistance',
          recommendedApproach: 'MI'
        }
      };

      const result = techniqueSelector.selectApproachAndTechnique(patientAnalysis);
      
      expect(result.id).toBe('motivational-interviewing');
      // Should select techniques appropriate for resistant patients
      const appropriateTechniques = ['mi-reflective-listening', 'mi-rolling-resistance', 'mi-affirmations'];
      expect(appropriateTechniques).toContain(result.selectedTechnique.id);
    });

    it('should select appropriate CBT techniques for ready patients', () => {
      const patientAnalysis: PatientAnalysis = {
        sentiment: 'neutral',
        motivationLevel: 'high',
        engagementLevel: 'high',
        readinessScore: {
          score: 9,
          factors: {
            sentiment: { value: 'neutral', weight: 0.3, contribution: 0.6 },
            motivation: { value: 'high', weight: 0.4, contribution: 1.2 },
            engagement: { value: 'high', weight: 0.3, contribution: 0.9 }
          },
          reasoning: 'Ready for structured intervention',
          recommendedApproach: 'CBT'
        }
      };

      const result = techniqueSelector.selectApproachAndTechnique(patientAnalysis);
      
      expect(result.id).toBe('cognitive-behavioral-therapy');
      // Should select CBT techniques
      const cbtTechniques = ['cbt-thought-challenging', 'cbt-behavioral-activation', 'cbt-problem-solving', 'cbt-cognitive-restructuring'];
      expect(cbtTechniques).toContain(result.selectedTechnique.id);
    });

    it('should avoid recently used techniques when possible', () => {
      const patientAnalysis: PatientAnalysis = {
        sentiment: 'neutral',
        motivationLevel: 'medium',
        engagementLevel: 'medium',
        readinessScore: {
          score: 5,
          factors: {
            sentiment: { value: 'neutral', weight: 0.3, contribution: 0.6 },
            motivation: { value: 'medium', weight: 0.4, contribution: 0.8 },
            engagement: { value: 'medium', weight: 0.3, contribution: 0.6 }
          },
          reasoning: 'Moderate readiness',
          recommendedApproach: 'MI'
        }
      };

      const context = {
        messageCount: 5,
        previousTechniques: ['mi-open-questions', 'mi-reflective-listening'],
        sessionPhase: 'middle' as const
      };

      const result = techniqueSelector.selectApproachAndTechnique(patientAnalysis, context);
      
      expect(result.selectedTechnique.id).not.toBe('mi-open-questions');
      expect(result.selectedTechnique.id).not.toBe('mi-reflective-listening');
    });
  });

  describe('Patient Analysis Integration', () => {
    it('should perform comprehensive patient analysis', async () => {
      const message = "I'm feeling really motivated to change and I want to tell you more about my situation. I think I'm ready to work on this.";
      
      const analysis = await openaiService.analyzePatient(message);
      
      expect(analysis.sentiment).toBe('positive');
      expect(analysis.motivationLevel).toBe('high');
      expect(analysis.engagementLevel).toBe('high');
      expect(analysis.readinessScore).toBeDefined();
      expect(analysis.readinessScore.score).toBeGreaterThanOrEqual(8);
      expect(analysis.readinessScore.recommendedApproach).toBe('CBT');
    });

    it('should handle error cases gracefully', async () => {
      // Mock an error in the analysis
      const originalAnalyzeSentiment = openaiService.analyzeSentiment;
      openaiService.analyzeSentiment = async () => {
        throw new Error('API Error');
      };

      const analysis = await openaiService.analyzePatient('test message');
      
      expect(analysis.sentiment).toBe('neutral');
      expect(analysis.motivationLevel).toBe('medium');
      expect(analysis.engagementLevel).toBe('medium');
      expect(analysis.readinessScore).toBeDefined();

      // Restore original method
      openaiService.analyzeSentiment = originalAnalyzeSentiment;
    });
  });

  describe('Technique Prompt Retrieval', () => {
    it('should return correct prompts for MI techniques', () => {
      const prompt = techniqueSelector.getTechniquePrompt('mi-open-questions');
      
      expect(prompt).toContain('open-ended questions');
      expect(prompt).toContain('encourage elaboration');
    });

    it('should return correct prompts for CBT techniques', () => {
      const prompt = techniqueSelector.getTechniquePrompt('cbt-thought-challenging');
      
      expect(prompt).toContain('identify and examine');
      expect(prompt).toContain('negative thoughts');
    });

    it('should return default prompt for unknown techniques', () => {
      const prompt = techniqueSelector.getTechniquePrompt('unknown-technique');
      
      expect(prompt).toContain('Respond therapeutically');
    });
  });
});
