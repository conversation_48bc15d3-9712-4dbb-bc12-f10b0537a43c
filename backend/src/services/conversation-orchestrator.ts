// Conversation Orchestrator for MiCA Therapy Simulation
import { v4 as uuidv4 } from 'uuid';
import { OpenAIService } from './openai.js';
import { TherapistAgentService } from './agents/therapist.js';
import { PatientAgentService } from './agents/patient.js';
import { ConversationContext, AgentResponse, WebSocketClient } from '../types/index.js';
import { ConversationConfig, defaultConversationConfig } from '../config/conversation.js';

export interface ConversationMessage {
  id: string;
  conversationId: string;
  sender: 'therapist' | 'patient';
  content: string;
  thinking: string;
  metadata: {
    confidence: number;
    processingTime: number;
    patientAnalysis?: {
      sentiment: 'positive' | 'negative' | 'neutral';
      motivationLevel: 'low' | 'medium' | 'high';
      engagementLevel: 'low' | 'medium' | 'high';
    };
  };
  timestamp: string;
}

export class ConversationOrchestrator {
  private openaiService: OpenAIService;
  private therapistAgent: TherapistAgentService;
  private patientAgent: PatientAgentService;
  private config: ConversationConfig;
  private context: ConversationContext;
  private clients: WebSocketClient[] = [];
  private isActive: boolean = false;
  private currentTurn: 'therapist' | 'patient' = 'therapist';

  constructor(conversationId?: string, config?: ConversationConfig) {
    this.config = config || defaultConversationConfig;
    
    // Initialize services
    this.openaiService = new OpenAIService();
    this.therapistAgent = new TherapistAgentService(this.openaiService, this.config.therapist);
    this.patientAgent = new PatientAgentService(this.openaiService, this.config.patient);

    // Initialize conversation context
    this.context = {
      id: conversationId || uuidv4(),
      messages: [],
      currentTurn: 0,
      maxTurns: this.config.conversation.maxTurns,
      status: 'active'
    };

    console.log(`🎭 Conversation Orchestrator initialized`);
    console.log(`🆔 Conversation ID: ${this.context.id}`);
    console.log(`🎯 Max turns: ${this.context.maxTurns}`);
  }

  /**
   * Start a new conversation
   */
  async startConversation(): Promise<void> {
    console.log('🚀 Starting new conversation...');
    
    if (this.isActive) {
      console.warn('⚠️ Conversation already active');
      return;
    }

    this.isActive = true;
    this.context.status = 'active';
    this.currentTurn = 'therapist';

    // Broadcast conversation start
    this.broadcastToClients({
      type: 'conversation_started',
      data: {
        conversationId: this.context.id,
        maxTurns: this.context.maxTurns
      },
      timestamp: new Date().toISOString()
    });

    // Generate and send initial therapist greeting
    await this.processTherapistTurn();
  }

  /**
   * Process therapist turn
   */
  private async processTherapistTurn(): Promise<void> {
    console.log('👩‍⚕️ Processing therapist turn...');

    try {
      let response: AgentResponse;

      if (this.context.messages.length === 0) {
        // Initial greeting
        response = await this.therapistAgent.generateInitialGreeting();
      } else {
        // Response to patient
        const lastPatientMessage = this.getLastPatientMessage();
        if (!lastPatientMessage) {
          console.error('❌ No patient message found for therapist response');
          return;
        }
        response = await this.therapistAgent.generateResponse(lastPatientMessage.content, this.context);
      }

      // Create message
      const message = this.createMessage('therapist', response);
      this.context.messages.push(message);
      this.context.currentTurn++;

      // Broadcast message
      this.broadcastMessage(message);

      // Schedule patient response if conversation should continue
      if (this.shouldContinueConversation()) {
        this.currentTurn = 'patient';
        setTimeout(() => {
          this.processPatientTurn();
        }, this.config.conversation.responseDelay.patient);
      } else {
        await this.endConversation();
      }

    } catch (error) {
      console.error('❌ Error processing therapist turn:', error);
      this.handleError('Failed to generate therapist response');
    }
  }

  /**
   * Process patient turn
   */
  private async processPatientTurn(): Promise<void> {
    console.log('👤 Processing patient turn...');

    try {
      const lastTherapistMessage = this.getLastTherapistMessage();
      if (!lastTherapistMessage) {
        console.error('❌ No therapist message found for patient response');
        return;
      }

      const response = await this.patientAgent.generateResponse(lastTherapistMessage.content, this.context);

      // Create message
      const message = this.createMessage('patient', response);
      this.context.messages.push(message);
      this.context.currentTurn++;

      // Broadcast message
      this.broadcastMessage(message);

      // Schedule therapist response if conversation should continue
      if (this.shouldContinueConversation()) {
        this.currentTurn = 'therapist';
        setTimeout(() => {
          this.processTherapistTurn();
        }, this.config.conversation.responseDelay.therapist);
      } else {
        await this.endConversation();
      }

    } catch (error) {
      console.error('❌ Error processing patient turn:', error);
      this.handleError('Failed to generate patient response');
    }
  }

  /**
   * Create a conversation message
   */
  private createMessage(sender: 'therapist' | 'patient', response: AgentResponse): ConversationMessage {
    return {
      id: uuidv4(),
      conversationId: this.context.id,
      sender,
      content: response.message,
      thinking: response.thinking,
      metadata: response.metadata,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Check if conversation should continue
   */
  private shouldContinueConversation(): boolean {
    if (!this.isActive) return false;
    if (this.context.currentTurn >= this.context.maxTurns) return false;
    if (this.context.status !== 'active') return false;
    return true;
  }

  /**
   * End the conversation
   */
  async endConversation(): Promise<void> {
    console.log('🏁 Ending conversation...');
    
    this.isActive = false;
    this.context.status = 'completed';

    // Broadcast conversation end
    this.broadcastToClients({
      type: 'conversation_ended',
      data: {
        conversationId: this.context.id,
        totalTurns: this.context.currentTurn,
        reason: this.context.currentTurn >= this.context.maxTurns ? 'max_turns_reached' : 'manual_end'
      },
      timestamp: new Date().toISOString()
    });

    console.log(`✅ Conversation ended after ${this.context.currentTurn} turns`);
  }

  /**
   * Pause the conversation
   */
  pauseConversation(): void {
    console.log('⏸️ Pausing conversation...');
    this.isActive = false;
    this.context.status = 'paused';

    this.broadcastToClients({
      type: 'conversation_paused',
      data: { conversationId: this.context.id },
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Resume the conversation
   */
  resumeConversation(): void {
    console.log('▶️ Resuming conversation...');
    this.isActive = true;
    this.context.status = 'active';

    this.broadcastToClients({
      type: 'conversation_resumed',
      data: { conversationId: this.context.id },
      timestamp: new Date().toISOString()
    });

    // Continue with the appropriate turn
    if (this.currentTurn === 'therapist') {
      this.processTherapistTurn();
    } else {
      this.processPatientTurn();
    }
  }

  /**
   * Get last patient message
   */
  private getLastPatientMessage(): ConversationMessage | null {
    for (let i = this.context.messages.length - 1; i >= 0; i--) {
      const message = this.context.messages[i];
      if (message && message.sender === 'patient') {
        return message as ConversationMessage;
      }
    }
    return null;
  }

  /**
   * Get last therapist message
   */
  private getLastTherapistMessage(): ConversationMessage | null {
    for (let i = this.context.messages.length - 1; i >= 0; i--) {
      const message = this.context.messages[i];
      if (message && message.sender === 'therapist') {
        return message as ConversationMessage;
      }
    }
    return null;
  }

  /**
   * Broadcast message to all connected clients
   */
  private broadcastMessage(message: ConversationMessage): void {
    console.log(`📢 Broadcasting ${message.sender} message to ${this.clients.length} clients`);
    
    const wsMessage = {
      type: 'conversation_message',
      data: {
        message: {
          sender: message.sender,
          content: message.content,
          timestamp: message.timestamp
        },
        thinking: {
          agent: message.sender,
          content: message.thinking,
          timestamp: message.timestamp
        },
        metadata: message.metadata
      },
      conversationId: this.context.id,
      timestamp: message.timestamp
    };

    this.broadcastToClients(wsMessage);
  }

  /**
   * Broadcast to all connected clients
   */
  private broadcastToClients(message: any): void {
    this.clients.forEach(client => {
      try {
        if (client.socket.readyState === 1) { // WebSocket.OPEN
          client.socket.send(JSON.stringify(message));
        }
      } catch (error) {
        console.error('❌ Error broadcasting to client:', error);
      }
    });
  }

  /**
   * Handle errors
   */
  private handleError(message: string): void {
    console.error(`❌ Orchestrator error: ${message}`);
    
    this.broadcastToClients({
      type: 'error',
      message,
      conversationId: this.context.id,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Add WebSocket client
   */
  addClient(client: WebSocketClient): void {
    this.clients.push(client);
    console.log(`👥 Client added to conversation ${this.context.id}. Total clients: ${this.clients.length}`);
  }

  /**
   * Remove WebSocket client
   */
  removeClient(clientId: string): void {
    this.clients = this.clients.filter(client => client.id !== clientId);
    console.log(`👥 Client removed from conversation ${this.context.id}. Total clients: ${this.clients.length}`);
  }

  /**
   * Get conversation context
   */
  getContext(): ConversationContext {
    return { ...this.context };
  }

  /**
   * Get conversation messages
   */
  getMessages(): ConversationMessage[] {
    return [...this.context.messages];
  }

  /**
   * Check if conversation is active
   */
  isConversationActive(): boolean {
    return this.isActive;
  }

  /**
   * Clear conversation history
   */
  clearHistory(): void {
    console.log('🗑️ Clearing conversation history...');
    
    this.context.messages = [];
    this.context.currentTurn = 0;
    this.therapistAgent.clearHistory();
    this.patientAgent.clearHistory();
    
    this.broadcastToClients({
      type: 'conversation_cleared',
      data: { conversationId: this.context.id },
      timestamp: new Date().toISOString()
    });
  }
}
