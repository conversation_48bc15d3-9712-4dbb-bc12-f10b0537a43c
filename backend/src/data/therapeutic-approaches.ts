// Therapeutic Approaches Framework for MiCA Therapy Simulation
// Comprehensive definitions for CBT and Motivational Interviewing approaches

export interface TherapeuticTechnique {
  id: string;
  name: string;
  description: string;
  prompt: string;
  selectionCriteria: {
    patientStates: string[];
    situationalFactors: string[];
    contraindications?: string[];
  };
  expectedOutcomes: string[];
}

export interface TherapeuticApproach {
  id: string;
  name: string;
  description: string;
  philosophy: string;
  readinessThreshold: {
    min: number;
    max: number;
  };
  techniques: TherapeuticTechnique[];
  selectionCriteria: {
    patientReadiness: string;
    appropriateFor: string[];
    goals: string[];
  };
}

export interface ReadinessAssessment {
  score: number; // 1-10 scale
  factors: {
    sentiment: {
      value: 'positive' | 'negative' | 'neutral';
      weight: number;
      contribution: number;
    };
    motivation: {
      value: 'low' | 'medium' | 'high';
      weight: number;
      contribution: number;
    };
    engagement: {
      value: 'low' | 'medium' | 'high';
      weight: number;
      contribution: number;
    };
  };
  reasoning: string;
  recommendedApproach: 'CBT' | 'MI';
}

// Motivational Interviewing Techniques
export const motivationalInterviewingTechniques: TherapeuticTechnique[] = [
  {
    id: 'mi-open-questions',
    name: 'Open-Ended Questions',
    description: 'Questions that encourage elaboration and exploration of thoughts and feelings',
    prompt: 'Ask open-ended questions that help the patient explore their thoughts, feelings, and motivations. Avoid yes/no questions. Focus on understanding their perspective and encouraging self-reflection.',
    selectionCriteria: {
      patientStates: ['guarded', 'withdrawn', 'ambivalent', 'exploring'],
      situationalFactors: ['initial sessions', 'resistance to change', 'need for exploration']
    },
    expectedOutcomes: ['increased self-reflection', 'deeper exploration', 'enhanced engagement']
  },
  {
    id: 'mi-reflective-listening',
    name: 'Reflective Listening',
    description: 'Reflecting back what the patient has said to demonstrate understanding and encourage further exploration',
    prompt: 'Reflect back what the patient has shared, demonstrating deep understanding. Use phrases like "It sounds like..." or "What I hear you saying is..." to show you are listening and to encourage them to continue.',
    selectionCriteria: {
      patientStates: ['emotional', 'confused', 'conflicted', 'sharing'],
      situationalFactors: ['building rapport', 'validating emotions', 'encouraging openness']
    },
    expectedOutcomes: ['increased trust', 'feeling heard', 'continued sharing']
  },
  {
    id: 'mi-affirmations',
    name: 'Affirmations',
    description: 'Recognizing and affirming the patient\'s strengths, efforts, and positive qualities',
    prompt: 'Acknowledge and affirm the patient\'s strengths, efforts, or positive qualities. Focus on their resilience, courage in seeking help, or any positive steps they\'ve taken.',
    selectionCriteria: {
      patientStates: ['low self-esteem', 'discouraged', 'self-critical', 'making effort'],
      situationalFactors: ['building confidence', 'recognizing progress', 'encouraging hope']
    },
    expectedOutcomes: ['increased self-efficacy', 'improved mood', 'enhanced motivation']
  },
  {
    id: 'mi-summarizing',
    name: 'Summarizing',
    description: 'Pulling together key themes and reflecting them back to reinforce important points',
    prompt: 'Summarize the key themes and important points from what the patient has shared. This helps consolidate their thoughts and shows you understand their situation comprehensively.',
    selectionCriteria: {
      patientStates: ['scattered thoughts', 'multiple concerns', 'processing'],
      situationalFactors: ['end of session', 'clarifying priorities', 'consolidating insights']
    },
    expectedOutcomes: ['clarity of thought', 'prioritized concerns', 'sense of progress']
  },
  {
    id: 'mi-change-talk',
    name: 'Eliciting Change Talk',
    description: 'Encouraging the patient to express their own motivations and reasons for change',
    prompt: 'Ask questions that help the patient express their own reasons for change, their hopes, and their concerns about their current situation. Focus on eliciting their internal motivation.',
    selectionCriteria: {
      patientStates: ['ambivalent', 'considering change', 'motivated'],
      situationalFactors: ['exploring motivation', 'building commitment', 'preparing for action']
    },
    expectedOutcomes: ['increased intrinsic motivation', 'commitment to change', 'self-advocacy']
  },
  {
    id: 'mi-rolling-resistance',
    name: 'Rolling with Resistance',
    description: 'Avoiding confrontation and instead exploring the patient\'s perspective when they show resistance',
    prompt: 'When the patient shows resistance or defensiveness, avoid arguing. Instead, explore their perspective with curiosity and respect. Acknowledge their autonomy and right to make their own decisions.',
    selectionCriteria: {
      patientStates: ['resistant', 'defensive', 'argumentative', 'ambivalent'],
      situationalFactors: ['confronting resistance', 'maintaining rapport', 'respecting autonomy']
    },
    expectedOutcomes: ['reduced defensiveness', 'maintained rapport', 'continued engagement']
  }
];

// Cognitive Behavioral Therapy Techniques
export const cbtTechniques: TherapeuticTechnique[] = [
  {
    id: 'cbt-thought-challenging',
    name: 'Thought Challenging',
    description: 'Helping patients identify and examine the validity of negative or unhelpful thoughts',
    prompt: 'Help the patient identify specific negative thoughts and examine the evidence for and against them. Ask questions like "What evidence supports this thought?" and "Are there alternative ways to view this situation?"',
    selectionCriteria: {
      patientStates: ['negative thinking', 'catastrophizing', 'self-critical', 'ready for change'],
      situationalFactors: ['cognitive distortions present', 'patient ready for challenge', 'specific thoughts identified']
    },
    expectedOutcomes: ['balanced thinking', 'reduced negative thoughts', 'improved mood']
  },
  {
    id: 'cbt-behavioral-activation',
    name: 'Behavioral Activation',
    description: 'Encouraging engagement in meaningful and pleasurable activities to improve mood and functioning',
    prompt: 'Explore activities that the patient used to enjoy or might find meaningful. Help them plan specific, achievable steps to re-engage with these activities, starting small and building up.',
    selectionCriteria: {
      patientStates: ['depressed', 'withdrawn', 'inactive', 'motivated for change'],
      situationalFactors: ['behavioral patterns identified', 'ready for action', 'specific goals possible']
    },
    expectedOutcomes: ['increased activity', 'improved mood', 'sense of accomplishment']
  },
  {
    id: 'cbt-problem-solving',
    name: 'Problem-Solving',
    description: 'Systematic approach to identifying problems and developing practical solutions',
    prompt: 'Help the patient break down their problems into specific, manageable parts. Guide them through brainstorming solutions, evaluating options, and creating action plans.',
    selectionCriteria: {
      patientStates: ['overwhelmed', 'stuck', 'practical problems', 'ready for solutions'],
      situationalFactors: ['specific problems identified', 'patient ready for action', 'practical solutions possible']
    },
    expectedOutcomes: ['clearer problem definition', 'practical solutions', 'increased confidence']
  },
  {
    id: 'cbt-cognitive-restructuring',
    name: 'Cognitive Restructuring',
    description: 'Systematic process of identifying, challenging, and replacing unhelpful thought patterns',
    prompt: 'Guide the patient through identifying thought patterns, examining their accuracy and helpfulness, and developing more balanced, realistic alternatives.',
    selectionCriteria: {
      patientStates: ['distorted thinking', 'negative patterns', 'ready for change', 'insightful'],
      situationalFactors: ['patterns identified', 'patient engaged', 'ready for cognitive work']
    },
    expectedOutcomes: ['improved thought patterns', 'better emotional regulation', 'increased insight']
  },
  {
    id: 'cbt-exposure-planning',
    name: 'Exposure Planning',
    description: 'Gradual, systematic approach to facing feared situations or thoughts',
    prompt: 'Help the patient identify specific fears or avoidance behaviors and create a gradual plan to face them in a safe, controlled way. Start with less threatening situations and build up.',
    selectionCriteria: {
      patientStates: ['anxious', 'avoidant', 'ready for challenge', 'motivated'],
      situationalFactors: ['specific fears identified', 'patient committed', 'safe environment']
    },
    expectedOutcomes: ['reduced avoidance', 'decreased anxiety', 'increased confidence']
  },
  {
    id: 'cbt-homework-assignment',
    name: 'Homework Assignment',
    description: 'Specific tasks or exercises for the patient to practice between sessions',
    prompt: 'Collaborate with the patient to identify specific, achievable tasks they can work on between sessions. Make sure the assignments are clear, relevant, and manageable.',
    selectionCriteria: {
      patientStates: ['engaged', 'motivated', 'ready for practice', 'committed'],
      situationalFactors: ['specific skills to practice', 'patient agreement', 'clear goals']
    },
    expectedOutcomes: ['skill practice', 'continued progress', 'increased self-efficacy']
  }
];

// Therapeutic Approaches Definitions
export const therapeuticApproaches: TherapeuticApproach[] = [
  {
    id: 'motivational-interviewing',
    name: 'Motivational Interviewing',
    description: 'A collaborative, goal-oriented style of communication designed to strengthen personal motivation and commitment to change',
    philosophy: 'Respects patient autonomy and uses their own motivations to facilitate change. Focuses on exploring and resolving ambivalence.',
    readinessThreshold: {
      min: 1,
      max: 7
    },
    techniques: motivationalInterviewingTechniques,
    selectionCriteria: {
      patientReadiness: 'Low to moderate readiness for change (scores 1-7)',
      appropriateFor: ['ambivalent patients', 'resistant to change', 'early stages of therapy', 'building motivation'],
      goals: ['increase motivation', 'build rapport', 'explore ambivalence', 'enhance engagement']
    }
  },
  {
    id: 'cognitive-behavioral-therapy',
    name: 'Cognitive Behavioral Therapy',
    description: 'A structured, goal-oriented approach that focuses on identifying and changing unhelpful thought patterns and behaviors',
    philosophy: 'Problems are maintained by unhelpful thinking patterns and behaviors. Change occurs through identifying and modifying these patterns.',
    readinessThreshold: {
      min: 8,
      max: 10
    },
    techniques: cbtTechniques,
    selectionCriteria: {
      patientReadiness: 'High readiness for change (scores 8-10)',
      appropriateFor: ['motivated patients', 'ready for active work', 'specific problems identified', 'goal-oriented'],
      goals: ['change thought patterns', 'modify behaviors', 'develop coping skills', 'solve specific problems']
    }
  }
];

// Readiness Score Calculation Weights
export const readinessWeights = {
  sentiment: {
    positive: 3,
    neutral: 2,
    negative: 1,
    weight: 0.3
  },
  motivation: {
    high: 3,
    medium: 2,
    low: 1,
    weight: 0.4
  },
  engagement: {
    high: 3,
    medium: 2,
    low: 1,
    weight: 0.3
  }
};
