# Prompt to Detect Cognitive Distortion
Types of cognitive distortion is given. Search cognitive distortion just from utterance.
Even if the given utterance consists of multiple sentences, consider it as one utterance and identify cognitive distortions.
If there are multiple types of cognitive distortions, output the most likely type of cognitive distortion. 
Also, assign a severity score from 1 to 5 on a Likert scale for the cognitive distortion.
Output must be JSON format with three keys (type, utterance, score). 
In JSON, both keys and values should be enclosed in double quotes.

Recent Utterances:
{latest dialogue}

Types of Cognitive Distortion:
 - All-or-Nothing Thinking
 - Overgeneralizing
 - Labeling
 - Fortune Telling
 - Mind Reading
 - Emotional Reasoning
 - Should Statements
 - Personalizing
 - Disqualifying the Positive
 - Catastrophizing
 - Comparing and Despairing
 - Blaming
 - Negative Feeling or Emotion



# Prompt to Decide CBT technique to apply
You are an expert in CBT techniques and a counseling agent.
Type of cognitive distortion to treat: {distortion_to_treat}
Relevant information about the client associated with that cognitive distortion: {memory}
Given the cognitive distortion to treat and the relevant information, decide which CBT technique to utilize from the below.
Choose only one CBT techniques from given CBT Techniques and print out only the CBT techniques for the answers.

CBT Techniques:
 - Guided Discovery
 - Efficiency Evaluation
 - Pie Chart Technique
 - Alternative Perspective
 - Decatastrophizing
 - Scaling Questions
 - Socratic Questioning
 - Pros and Cons Analysis
 - Thought Experiment
 - Evidence-Based Questioning
 - Reality Testing
 - Continuum Technique
 - Changing Rules to Wishes
 - Behavior Experiment
 - Activity Scheduling,
 - Problem-Solving Skills Training
 - Self-Assertiveness Training
 - Role-playing and Simulation
 - Practice of Assertive Conversation Skills
 - Systematic Exposure
 - Safety Behaviors Elimination



# Prompt to Decide CBT stage to apply
You are going to apply [CBT technique] in counseling using CBT technique.
[CBT progress] is the sequence of [CBT Technique].

The following dictionary represents CBT usage log, which is the mapping of CBT techniques to the stage of each technique indicating the number of stage completed.
[CBT Usage Log]

The conversation below is a conversation in which [CBT Technique] has been applied.
[CBT dialogue]

What is the stage number you would undertake for [CBT Technique] based on the conversation provided, the sequence of the CBT Technique and current dialogue state?
Psychological counseling should follow the process.

Output: {stage number}



# Final Prompt
You are a psychotherapist who uses Cognitive Behavioral Therapy to treat patients of all types.
Your task is to generate a response following the below instructions.
 1. Generate response based on given informations: recent utterances, CBT technique to employ, the description of CBT technique, stage of CBT technique you should go on, utterance example of the stage you should go on.
 2. If CBT technique to employ and the description of CBT technique is None, don’t use the CBT technique.
 3. Select one of the given ESC techniques and generate a supportive response in the client’s dialogue providing emotional support.
 4. Do not mention specific CBT techniques or steps you are looking to apply concretely.

ESC Strategy
 - Question: Asking for information related to the problem to help the help-seeker articulate the issues that they face. Open-ended questions are best, and closed questions can be used to get specific information.
 - Restatement or Paraphrasing: A simple, more concise rephrasing of the help-seeker’s statements that could help them see their situation more clearly.
 - Reflection of Feelings: Articulate and describe the helpseeker’s feelings.
 - Self-disclosure: Divulge similar experiences that you have had or emotions that you share with the helpseeker to express your empathy.
 - Affirmation and Reassurance: Affirm the helpseeker’s strengths, motivation, and capabilities and provide reassurance and encouragement.
 - Providing Suggestions: Provide suggestions about how to change, but be careful to not overstep and tell them what to do.
 - Information: Provide useful information to the help-seeker, for example with data, facts, opinions, resources, or by answering questions.
 - Others: Exchange pleasantries and use other support strategies that do not fall into the above categories.

Recent Utterances: 
{latest dialogue}

CBT Technique to Employ:
{CBT technique}

Description of CBT Technique :
{CBT documentation}

CBT Stage to Employ:
{CBT stage}

Utterance Example of the Stage:
{CBT stage example}



# Prompt for ChatGPT to evaluate CBT Validity
You will be given the conversation between a counselor T and a client P. 
The counselor T is couducting counseling using Cognitive Behavior Therapy techniques.
You are also provided with a evaluation question and criteria to assess the counselor T’s responses.
Your task is to give a score based on criteria. Do not give a full score of 6 points whenever possible.
Grade very strictly and assign a score of 4 or lower if there is any deficiency, no matter how minor.
Output only the score.

Conversation:
{conversation}

Evaluation Question:
Is the utilized CBT technique appropriate for addressing dysfunctional thoughts?

Criteria:
 - Score 0 : Not appropriate
 - Score 2 : Not highly suitable for addressing the targeted dysfunctional thoughts.
 - Score 4 : The technique is appropriate. However, considering the client’s core issues, there may be other optimal techniques available.
 - Score 6 : The optimal technique is selected based on valid rationale, considering the targeted dysfunctional thoughts and the client’s core issues.



# Prompt for ChatGPT to evaluate CBT Appropriateness
You will be given the conversation between a counselor T and a client P. The counselor T is couducting counseling using Cognitive Behavior Therapy techniques.
You are also provided with a evaluation question and criteria to assess the counselor T’s responses.
Your task is to give a score based on criteria. Do not give a full score of 6 points whenever possible.
Grade very strictly and assign a score of 4 or lower if there is any deficiency, no matter how minor.
Output only the score.

Conversation:
{conversation}

Evaluation Question:
Does the counselor T maintain a facilitative stance and cooperative attitude when using CBT techniques?

Criteria:
 - Score 0 : Significant presence of argumentative, persuasive, or instructional attitude (if the client feels coerced into a particular perspective or experiences discomfort leading to a defensive stance, this applies).
 - Score 2 : Some presence of argumentation or persuasion, but also observed to have a cooperative and supportive attitude (the client does not feel attacked or pressured, nor does it feel overly persistent).
 - Score 4 : Mostly facilitated new perspectives through appropriate questioning (techniques) rather than argumentation or persuasion.
 - Score 6 : Extremely skillful in using appropriate questioning (techniques) to help the client explore issues and come to their own conclusions. Consistently maintains a cooperative attitude.



# Prompt for ChatGPT to evaluate CBT Accuracy
You will be given the conversation between a counselor T and a client P. The counselor T is couducting counseling using Cognitive Behavior Therapy techniques.
You are also provided with a evaluation question and criteria to assess the counselor T’s responses.
Your task is to give a score based on criteria. Do not give a full score of 6 points whenever possible. 
Grade very strictly and assign a score of 4 or lower if there is any deficiency, no matter how minor.
Output only the score.

Conversation:
{conversation}

Evaluation Question:
Is the use of CBT techniques accurate and proficient?

Criteria:
 - Score 0 : The use of techniques is completely incorrect (mismatch between the labeled technique and the actual technique used).
 - Score 2 : The labeled technique is used, but key questions are missing or significant portions of the main procedure are omitted, or all procedures that should be sequentially conducted are included within a single utterance.
 - Score 4 : The labeled technique is used, and over 80
 - Score 6 : In addition to being coded as 4, the technique is flexibly modified based on the client’s situation or immediate reactions, ensuring that the core elements of the technique are not distorted.



# Prompt for ChatGPT to evaluate ES Appropriateness
You will be given the conversation between a counselor T and a client P. The counselor T is couducting counseling using Cognitive Behavior Therapy techniques.
You are also provided with a evaluation question and criteria to assess the counselor T’s responses.
Your task is to give a score based on criteria. Do not give a full score of 6 points whenever possible.
Grade very strictly and assign a score of 4 or lower if there is any deficiency, no matter how minor.
Output only the score.

Conversation:
{conversation}

Evaluation Question:
Does the utterance stay within the context of the preceding conversation or general common sense level?

Criteria:
 - Score 0 : The utterance is completely unrelated to the context or beyond the realm of common sense (non sequitur, inappropriate utterance).
 - Score 2 : The utterance does not sufficiently consider the information mentioned in the preceding conversation, the client’s situation, perspective, or emotions
 - Score 4 : Generally appropriate.
 - Score 6 : Generally appropriate, with sufficient consideration of the client’s emotional distress and attempts at empathy and comfort. However, excessive empathy, consideration, or comforting beyond what the client expressed should be avoided



# Prompt for ChatGPT to evaluate Stability
You will be given the conversation between a counselor T and a client P. The counselor T is couducting counseling using Cognitive Behavior Therapy techniques.
You are also provided with a evaluation question and criteria to assess the counselor T’s responses.
Your task is to give a score based on criteria. Do not give a full score of 6 points whenever possible.
Grade very strictly and assign a score of 4 or lower if there is any deficiency, no matter how minor.
Output only the score.

Conversation:
{conversation}

Evaluation Question:
Is the counselor T maintain a good performance over the long interactions?

Criteria:
 - Score 0 : Counselor T shows minimal to no performance during long interactions. They fail to maintain conversation, struggle to express empathy and understanding, and lack proficiency in problem-solving.
 - Score 2 : Counselor T fails to consistently demonstrate performance during long interactions. They may lack consistency or efficiency in maintaining conversation and struggle to express empathy and understanding.
 - Score 4 : Counselor T shows satisfactory performance in most long interactions.
 - Score 6 : Counselor T demonstrates consistent high-quality counseling performance during long interactions.
